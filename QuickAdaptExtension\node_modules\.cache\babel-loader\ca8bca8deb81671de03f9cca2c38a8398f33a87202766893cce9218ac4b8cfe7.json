{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const contentRef = useRef(\"\");\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // State to track content for dynamic icon positioning for each RTE\n  const [contentStates, setContentStates] = useState(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\n  const isContentEmpty = content => {\n    if (!content) return true;\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    return textContent.length === 0;\n  };\n\n  // Helper function to check if content is scrollable for specific RTE\n  const isContentScrollable = rteId => {\n    const containerRef = getContainerRef(rteId);\n    if (containerRef !== null && containerRef !== void 0 && containerRef.current) {\n      const workplace = containerRef.current.querySelector('.jodit-workplace');\n      if (workplace) {\n        return workplace.scrollHeight > workplace.clientHeight;\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning for specific RTE\n  const updateContentState = (rteId, content) => {\n    const isEmpty = isContentEmpty(content);\n    const isScrollable = isContentScrollable(rteId);\n    setContentStates(prev => {\n      const newStates = new Map(prev);\n      newStates.set(rteId, {\n        isEmpty,\n        isScrollable\n      });\n      return newStates;\n    });\n  };\n\n  // Get content state for specific RTE\n  const getContentState = rteId => {\n    return contentStates.get(rteId) || {\n      isEmpty: true,\n      isScrollable: false\n    };\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Update content state for dynamic icon positioning\n    updateContentState(containerId, newContent);\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n      // Don't set editing state, just show toolbar\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  // Memoize Jodit config to prevent re-renders and focus loss\n  const getJoditConfig = rteId => {\n    const toolbarVisible = toolbarVisibleRTEId === rteId;\n    return {\n      readonly: false,\n      direction: isRtlDirection ? 'rtl' : 'ltr',\n      language: 'en',\n      // Hide main toolbar by default, controlled by toolbarVisible state\n      toolbar: toolbarVisible,\n      // Enable inline toolbar for text selection\n      toolbarSticky: false,\n      toolbarAdaptive: false,\n      // Inline toolbar width configuration\n      toolbarButtonSize: 'small',\n      toolbarInlineWidth: 500,\n      toolbarInlineMaxWidth: 600,\n      toolbarInlineMinWidth: 450,\n      // Additional popup configuration for inline toolbar\n      popup: {\n        selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\n        toolbar: {\n          width: 500,\n          maxWidth: 600,\n          minWidth: 450\n        }\n      },\n      showCharsCounter: false,\n      showWordsCounter: false,\n      showXPathInStatusbar: false,\n      statusbar: false,\n      pastePlain: true,\n      askBeforePasteHTML: false,\n      askBeforePasteFromWord: false,\n      buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n        name: 'more',\n        iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n        list: ['image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n      }],\n      autofocus: false,\n      // Enable auto-resize behavior\n      height: 'auto',\n      minHeight: toolbarVisible ? 150 : 28,\n      // 150px when toolbar visible, 28px when hidden\n      maxHeight: 180,\n      // Fix dialog positioning by setting popup root to document body\n      popupRoot: document.body,\n      // Ensure dialogs appear in correct position\n      zIndex: 100000,\n      globalFullSize: false,\n      // Add custom CSS to ensure text is visible\n      style: {\n        color: '#000000 !important',\n        backgroundColor: '#ffffff',\n        fontFamily: 'Poppins, sans-serif'\n      },\n      // Override editor styles to ensure text visibility\n      editorCssClass: 'jodit-popup-editor',\n      // Set default content styling\n      enter: 'p',\n      // Fix link dialog positioning\n      link: {\n        followOnDblClick: false,\n        processVideoLink: true,\n        processPastedLink: true,\n        openInNewTabCheckbox: true,\n        noFollowCheckbox: false,\n        modeClassName: 'input'\n      },\n      // Dialog configuration\n      dialog: {\n        zIndex: 100001\n      },\n      events: {\n        onPaste: handlePaste // Attach custom onPaste handler\n      },\n      controls: {\n        font: {\n          list: {\n            \"Poppins, sans-serif\": \"Poppins\",\n            \"Roboto, sans-serif\": \"Roboto\",\n            \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n            \"Open Sans, sans-serif\": \"Open Sans\",\n            \"Calibri, sans-serif\": \"Calibri\",\n            \"Century Gothic, sans-serif\": \"Century Gothic\"\n          }\n        }\n      }\n    };\n  };\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          // Hover effect to show action buttons\n          \"&:hover .rte-action-buttons\": {\n            opacity: 1\n          },\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"calc(100vh - 400px) !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          // Fix Jodit dialog positioning - target correct classes\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          // Fix for link dialog specifically\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: getJoditConfig(id),\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rte-action-buttons\",\n            style: {\n              position: \"absolute\",\n              top: \"8px\",\n              right: \"15px\",\n              display: \"flex\",\n              gap: \"4px\",\n              opacity: 0,\n              transition: \"opacity 0.2s ease-in-out\",\n              zIndex: 1000,\n              pointerEvents: \"none\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                handleCloneContainer(item.id);\n              },\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                pointerEvents: \"auto\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                handleDeleteSection(item.id, rteId);\n              },\n              title: translate(\"Delete Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                pointerEvents: \"auto\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                toggleToolbar(id);\n              },\n              title: translate(\"Toggle Toolbar\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                pointerEvents: \"auto\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 29\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"22xqD1Xg0VnyPrrsRyCKJg/lHas=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"22xqD1Xg0VnyPrrsRyCKJg/lHas=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "contentRef", "isHovered", "setIsHovered", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "contentStates", "setContentStates", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "isContentEmpty", "content", "textContent", "replace", "trim", "length", "isContentScrollable", "containerRef", "workplace", "querySelector", "scrollHeight", "clientHeight", "updateContentState", "isEmpty", "isScrollable", "prev", "newStates", "getContentState", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "editor", "selection", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "getJoditConfig", "toolbarVisible", "readonly", "direction", "language", "toolbar", "toolbarSticky", "toolbarAdaptive", "toolbarButtonSize", "toolbarInlineWidth", "toolbarInlineMaxWidth", "toolbarInlineMinWidth", "popup", "width", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "buttons", "name", "iconURL", "list", "autofocus", "height", "minHeight", "maxHeight", "popupRoot", "zIndex", "globalFullSize", "style", "color", "backgroundColor", "fontFamily", "editor<PERSON>s<PERSON><PERSON>", "enter", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "currentEditorRef", "sx", "display", "alignItems", "position", "opacity", "whiteSpace", "wordBreak", "overflow", "justifyContent", "top", "left", "transform", "background", "border", "borderRadius", "boxShadow", "className", "value", "config", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "right", "gap", "transition", "pointerEvents", "size", "onClick", "e", "stopPropagation", "disabled", "title", "dangerouslySetInnerHTML", "__html", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef, useMemo, useCallback } from \"react\";\r\nimport { Box, TextField, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n        const [isHovered, setIsHovered] = useState(false);\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // State to track content for dynamic icon positioning for each RTE\r\n        const [contentStates, setContentStates] = useState<Map<string, { isEmpty: boolean, isScrollable: boolean }>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\r\n        const isContentEmpty = (content: string): boolean => {\r\n            if (!content) return true;\r\n            // Remove HTML tags and check if there's actual text content\r\n            const textContent = content.replace(/<[^>]*>/g, '').trim();\r\n            return textContent.length === 0;\r\n        };\r\n\r\n        // Helper function to check if content is scrollable for specific RTE\r\n        const isContentScrollable = (rteId: string): boolean => {\r\n            const containerRef = getContainerRef(rteId);\r\n            if (containerRef?.current) {\r\n                const workplace = containerRef.current.querySelector('.jodit-workplace');\r\n                if (workplace) {\r\n                    return workplace.scrollHeight > workplace.clientHeight;\r\n                }\r\n            }\r\n            return false;\r\n        };\r\n\r\n        // Update content state for dynamic icon positioning for specific RTE\r\n        const updateContentState = (rteId: string, content: string) => {\r\n            const isEmpty = isContentEmpty(content);\r\n            const isScrollable = isContentScrollable(rteId);\r\n\r\n            setContentStates(prev => {\r\n                const newStates = new Map(prev);\r\n                newStates.set(rteId, { isEmpty, isScrollable });\r\n                return newStates;\r\n            });\r\n        };\r\n\r\n        // Get content state for specific RTE\r\n        const getContentState = (rteId: string) => {\r\n            return contentStates.get(rteId) || { isEmpty: true, isScrollable: false };\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Update content state for dynamic icon positioning\r\n            updateContentState(containerId, newContent);\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n        const toggleToolbar = (rteId: string) => {\r\n            if (toolbarVisibleRTEId === rteId) {\r\n                setToolbarVisibleRTEId(null);\r\n            } else {\r\n                setToolbarVisibleRTEId(rteId);\r\n                // Don't set editing state, just show toolbar\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    // Memoize Jodit config to prevent re-renders and focus loss\r\n    const getJoditConfig = (rteId: string) => {\r\n        const toolbarVisible = toolbarVisibleRTEId === rteId;\r\n\r\n        return {\r\n            readonly: false,\r\n            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n            language: 'en',\r\n            // Hide main toolbar by default, controlled by toolbarVisible state\r\n            toolbar: toolbarVisible,\r\n            // Enable inline toolbar for text selection\r\n            toolbarSticky: false,\r\n            toolbarAdaptive: false,\r\n            // Inline toolbar width configuration\r\n            toolbarButtonSize: 'small' as const,\r\n            toolbarInlineWidth: 500,\r\n            toolbarInlineMaxWidth: 600,\r\n            toolbarInlineMinWidth: 450,\r\n            // Additional popup configuration for inline toolbar\r\n            popup: {\r\n                selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\r\n                toolbar: {\r\n                    width: 500,\r\n                    maxWidth: 600,\r\n                    minWidth: 450\r\n                }\r\n            },\r\n            showCharsCounter: false,\r\n            showWordsCounter: false,\r\n            showXPathInStatusbar: false,\r\n            statusbar: false,\r\n            pastePlain: true,\r\n            askBeforePasteHTML: false,\r\n            askBeforePasteFromWord: false,\r\n            buttons: [\r\n                'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n                'font', 'fontsize', 'link',\r\n                {\r\n                    name: 'more',\r\n                    iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n                    list: [\r\n                        'image', 'video', 'table',\r\n                        'align', 'undo', 'redo', '|',\r\n                        'hr', 'eraser', 'copyformat',\r\n                        'symbol', 'print', 'superscript', 'subscript', '|',\r\n                        'outdent', 'indent', 'paragraph',\r\n                    ]\r\n                }\r\n            ],\r\n            autofocus: false,\r\n            // Enable auto-resize behavior\r\n            height: 'auto',\r\n            minHeight: toolbarVisible ? 150 : 28, // 150px when toolbar visible, 28px when hidden\r\n            maxHeight: 180,\r\n            // Fix dialog positioning by setting popup root to document body\r\n            popupRoot: document.body,\r\n            // Ensure dialogs appear in correct position\r\n            zIndex: 100000,\r\n            globalFullSize: false,\r\n            // Add custom CSS to ensure text is visible\r\n            style: {\r\n                color: '#000000 !important',\r\n                backgroundColor: '#ffffff',\r\n                fontFamily: 'Poppins, sans-serif',\r\n            },\r\n            // Override editor styles to ensure text visibility\r\n            editorCssClass: 'jodit-popup-editor',\r\n            // Set default content styling\r\n            enter: 'p' as const,\r\n            // Fix link dialog positioning\r\n            link: {\r\n                followOnDblClick: false,\r\n                processVideoLink: true,\r\n                processPastedLink: true,\r\n                openInNewTabCheckbox: true,\r\n                noFollowCheckbox: false,\r\n                modeClassName: 'input' as const,\r\n            },\r\n            // Dialog configuration\r\n            dialog: {\r\n                zIndex: 100001,\r\n            },\r\n            events: {\r\n                onPaste: handlePaste, // Attach custom onPaste handler\r\n            },\r\n            controls: {\r\n                font: {\r\n                    list: {\r\n                        \"Poppins, sans-serif\": \"Poppins\",\r\n                        \"Roboto, sans-serif\": \"Roboto\",\r\n                        \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                        \"Open Sans, sans-serif\": \"Open Sans\",\r\n                        \"Calibri, sans-serif\": \"Calibri\",\r\n                        \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n                    }\r\n                }\r\n            }\r\n        };\r\n    };\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                // Hover effect to show action buttons\r\n                                \"&:hover .rte-action-buttons\": {\r\n                                    opacity: 1,\r\n                                },\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"calc(100vh - 400px) !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                },\r\n                                // Fix Jodit dialog positioning - target correct classes\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                // Fix for link dialog specifically\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Always show Jodit editor with fixed hover-only action buttons */}\r\n                            <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                <JoditEditor\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={getJoditConfig(id)}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n\r\n                                {/* Fixed hover-only action buttons at top-right corner */}\r\n                                <div\r\n                                    className=\"rte-action-buttons\"\r\n                                    style={{\r\n                                        position: \"absolute\",\r\n                                        top: \"8px\",\r\n                                        right: \"15px\",\r\n                                        display: \"flex\",\r\n                                        gap: \"4px\",\r\n                                        opacity: 0,\r\n                                        transition: \"opacity 0.2s ease-in-out\",\r\n                                        zIndex: 1000,\r\n                                        pointerEvents: \"none\"\r\n                                    }}\r\n                                >\r\n                                    {/* Clone Button */}\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            handleCloneContainer(item.id);\r\n                                        }}\r\n                                        disabled={isCloneDisabled}\r\n                                        title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                        sx={{\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            pointerEvents: \"auto\",\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                            style={{\r\n                                                opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                height: '16px',\r\n                                                width: '16px'\r\n                                            }}\r\n                                        />\r\n                                    </IconButton>\r\n\r\n                                    {/* Delete Button */}\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            handleDeleteSection(item.id, rteId);\r\n                                        }}\r\n                                        title={translate(\"Delete Section\")}\r\n                                        sx={{\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            pointerEvents: \"auto\",\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                            style={{\r\n                                                height: '16px',\r\n                                                width: '16px'\r\n                                            }}\r\n                                        />\r\n                                    </IconButton>\r\n\r\n                                    {/* Edit Button */}\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            toggleToolbar(id);\r\n                                        }}\r\n                                        title={translate(\"Toggle Toolbar\")}\r\n                                        sx={{\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            pointerEvents: \"auto\",\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                            style={{ height: '16px', width: '16px' }}\r\n                                        />\r\n                                    </IconButton>\r\n                                </div>\r\n                            </div>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAA8B,OAAO;AAC5F,SAASC,GAAG,EAAaC,UAAU,QAAQ,eAAe;AAC1D,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,6BAA6B;AAE5E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGd,UAAU,CAAAe,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM+C,UAAU,GAAG7C,MAAM,CAAS,EAAE,CAAC;EACrC,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMkD,UAAU,GAAGhD,MAAM,CAAoC,IAAIiD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGlD,MAAM,CAA+C,IAAIiD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAA2D,IAAImD,GAAG,CAAC,CAAC,CAAC;;EAEvH;EACA,MAAMI,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACN,UAAU,CAACO,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCN,UAAU,CAACO,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEzD,KAAK,CAAC6D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOV,UAAU,CAACO,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCJ,aAAa,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEzD,KAAK,CAAC6D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAOR,aAAa,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMO,cAAc,GAAIC,OAAe,IAAc;IACjD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,OAAOF,WAAW,CAACG,MAAM,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIb,KAAa,IAAc;IACpD,MAAMc,YAAY,GAAGR,eAAe,CAACN,KAAK,CAAC;IAC3C,IAAIc,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEb,OAAO,EAAE;MACvB,MAAMc,SAAS,GAAGD,YAAY,CAACb,OAAO,CAACe,aAAa,CAAC,kBAAkB,CAAC;MACxE,IAAID,SAAS,EAAE;QACX,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;MAC1D;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACnB,KAAa,EAAEQ,OAAe,KAAK;IAC3D,MAAMY,OAAO,GAAGb,cAAc,CAACC,OAAO,CAAC;IACvC,MAAMa,YAAY,GAAGR,mBAAmB,CAACb,KAAK,CAAC;IAE/CF,gBAAgB,CAACwB,IAAI,IAAI;MACrB,MAAMC,SAAS,GAAG,IAAI5B,GAAG,CAAC2B,IAAI,CAAC;MAC/BC,SAAS,CAACpB,GAAG,CAACH,KAAK,EAAE;QAAEoB,OAAO;QAAEC;MAAa,CAAC,CAAC;MAC/C,OAAOE,SAAS;IACpB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIxB,KAAa,IAAK;IACvC,OAAOH,aAAa,CAACQ,GAAG,CAACL,KAAK,CAAC,IAAI;MAAEoB,OAAO,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAM,CAAC;EAC7E,CAAC;;EAED;EACA5E,SAAS,CAAC,MAAM;IACZ,MAAMgF,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAAC3C,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAM4C,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACpB,aAAa,CAAC,cAAc,CAAC,cAAAW,qBAAA,uBAAtCA,qBAAA,CAAwCU,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMM,kBAAkB,IAAAV,sBAAA,GAAGQ,QAAQ,CAACpB,aAAa,CAAC,gBAAgB,CAAC,cAAAY,sBAAA,uBAAxCA,sBAAA,CAA0CS,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMO,sBAAsB,GAAGD,kBAAkB,MAAAT,sBAAA,GAAIO,QAAQ,CAACpB,aAAa,CAAC,sBAAsB,CAAC,cAAAa,sBAAA,uBAA9CA,sBAAA,CAAgDQ,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMQ,iBAAiB,GAAId,KAAK,CAACM,MAAM,CAAiBS,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAb,sBAAA,GAAGM,QAAQ,CAACpB,aAAa,CAAC,wBAAwB,CAAC,cAAAc,sBAAA,uBAAhDA,sBAAA,CAAkDO,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMY,qBAAqB,GAAIlB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMY,cAAc,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMa,mBAAmB,GAAGxC,eAAe,CAACnB,YAAY,CAAC;;MAEzD;MACA,IACI2D,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE7C,OAAO,IAC5B,CAAC6C,mBAAmB,CAAC7C,OAAO,CAACoC,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACG,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACd,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE9C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBE,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAED8C,QAAQ,CAACW,gBAAgB,CAAC,WAAW,EAAEtB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACY,mBAAmB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACtC,YAAY,CAAC,CAAC;EAElB1C,SAAS,CAAC,MAAM;IACZ,IAAI0C,YAAY,EAAE;MACd,MAAM8D,SAAS,GAAGlD,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI8D,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEhD,OAAO,EAAE;QACpBiD,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC/D,YAAY,CAAC,CAAC;EAIlB,MAAMgE,YAAY,GAAGA,CAACC,UAAkB,EAAEpD,KAAa,EAAEqD,WAAmB,KAAK;IAC7E9D,UAAU,CAACU,OAAO,GAAGmD,UAAU;;IAE/B;IACAjC,kBAAkB,CAACkC,WAAW,EAAED,UAAU,CAAC;;IAE3C;IACA,MAAME,gBAAgB,GAAGtE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAM4E,QAAQ,GAAGvE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAM8E,kBAAkB,GAAGD,QAAQ,IAAI5E,oBAAoB,KAAK,cAAc;IAC9E,MAAM8E,YAAY,GAAGF,QAAQ,IAAI5E,oBAAoB,KAAK,QAAQ;IAClE,MAAM+E,aAAa,GAAGH,QAAQ,KAAK5E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5GgF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpC5E,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpB2E,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;MAExC,IAAIuE,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAGlF,oBAAoB,CAACiF,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACAlF,qBAAqB,CAACsE,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAkB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAG1F,yBAAyB,CAACkF,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACA1F,0BAA0B,CAACuE,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;MACxC,MAAMgF,gBAAgB,IAAAQ,sBAAA,GAAG5F,oBAAoB,CAACiF,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACAlF,qBAAqB,CAACsE,WAAW,EAAED,UAAU,CAAC;QAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkCjF,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAAgG,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkClG,oBAAoB,OAAO,EAAE;UACxEmF,gBAAgB;UAChBT,WAAW;UACXyB,mBAAmB,GAAAH,sBAAA,GAAE9F,oBAAoB,CAACiF,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;YAAE4B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA/F,kBAAkB,CAAC+E,WAAW,EAAErD,KAAK,EAAEoD,UAAU,CAAC;MAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEArF,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAM0G,oBAAoB,GAAI5B,WAAmB,IAAK;IAClD;IACA,IAAIpF,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAAC6E,WAAW,CAAC;;IAE9B;IACA,IAAIrF,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAMkH,mBAAmB,GAAGA,CAAC7B,WAAmB,EAAErD,KAAY,KAAK;IAC/D;IACA,MAAMsD,gBAAgB,GAAGtE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAI2E,gBAAgB,EAAE;MAClB;MACA;MACA7E,eAAe,CAAC4E,WAAW,EAAErD,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAvB,eAAe,CAAC4E,WAAW,EAAErD,KAAK,CAAC;IACvC;;IAEA;IACAnC,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMqH,WAAW,GAAIzD,KAA2C,IAAK;IACjEA,KAAK,CAAC0D,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG3D,KAAK,CAAC2D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAInF,OAAe,IAAK;IACvC,IAAIrB,YAAY,EAAE;MACd,MAAM8D,SAAS,GAAGlD,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI8D,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEhD,OAAO,EAAE;QACpB,MAAM2F,MAAM,GAAI3C,SAAS,CAAChD,OAAO,CAAS2F,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACtF,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAMuF,aAAa,GAAI/F,KAAa,IAAK;IACrC,IAAIX,mBAAmB,KAAKW,KAAK,EAAE;MAC/BV,sBAAsB,CAAC,IAAI,CAAC;IAChC,CAAC,MAAM;MACHA,sBAAsB,CAACU,KAAK,CAAC;MAC7B;IACJ;EACJ,CAAC;EACD,MAAM,CAACgG,cAAc,EAAEC,iBAAiB,CAAC,GAAGzJ,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMyJ,GAAG,GAAG9D,QAAQ,CAAC+D,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACF;EACA,MAAMC,cAAc,GAAItG,KAAa,IAAK;IACtC,MAAMuG,cAAc,GAAGlH,mBAAmB,KAAKW,KAAK;IAEpD,OAAO;MACHwG,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAET,cAAc,GAAG,KAAK,GAAY,KAAc;MAC3DU,QAAQ,EAAE,IAAI;MACd;MACAC,OAAO,EAAEJ,cAAc;MACvB;MACAK,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,KAAK;MACtB;MACAC,iBAAiB,EAAE,OAAgB;MACnCC,kBAAkB,EAAE,GAAG;MACvBC,qBAAqB,EAAE,GAAG;MAC1BC,qBAAqB,EAAE,GAAG;MAC1B;MACAC,KAAK,EAAE;QACHrB,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;QAC5Gc,OAAO,EAAE;UACLQ,KAAK,EAAE,GAAG;UACVC,QAAQ,EAAE,GAAG;UACbC,QAAQ,EAAE;QACd;MACJ,CAAC;MACDC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,IAAI;MAChBC,kBAAkB,EAAE,KAAK;MACzBC,sBAAsB,EAAE,KAAK;MAC7BC,OAAO,EAAE,CACL,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;QACIC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,+DAA+D;QACxEC,IAAI,EAAE,CACF,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAClD,SAAS,EAAE,QAAQ,EAAE,WAAW;MAExC,CAAC,CACJ;MACDC,SAAS,EAAE,KAAK;MAChB;MACAC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE5B,cAAc,GAAG,GAAG,GAAG,EAAE;MAAE;MACtC6B,SAAS,EAAE,GAAG;MACd;MACAC,SAAS,EAAEjG,QAAQ,CAAC+D,IAAI;MACxB;MACAmC,MAAM,EAAE,MAAM;MACdC,cAAc,EAAE,KAAK;MACrB;MACAC,KAAK,EAAE;QACHC,KAAK,EAAE,oBAAoB;QAC3BC,eAAe,EAAE,SAAS;QAC1BC,UAAU,EAAE;MAChB,CAAC;MACD;MACAC,cAAc,EAAE,oBAAoB;MACpC;MACAC,KAAK,EAAE,GAAY;MACnB;MACAC,IAAI,EAAE;QACFC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,IAAI;QAC1BC,gBAAgB,EAAE,KAAK;QACvBC,aAAa,EAAE;MACnB,CAAC;MACD;MACAC,MAAM,EAAE;QACJf,MAAM,EAAE;MACZ,CAAC;MACDgB,MAAM,EAAE;QACJC,OAAO,EAAEpE,WAAW,CAAE;MAC1B,CAAC;MACDqE,QAAQ,EAAE;QACNC,IAAI,EAAE;UACFzB,IAAI,EAAE;YACF,qBAAqB,EAAE,SAAS;YAChC,oBAAoB,EAAE,QAAQ;YAC9B,2BAA2B,EAAE,eAAe;YAC5C,uBAAuB,EAAE,WAAW;YACpC,qBAAqB,EAAE,SAAS;YAChC,4BAA4B,EAAE;UAClC;QACJ;MACJ;IACJ,CAAC;EACL,CAAC;;EAEG;EACA,MAAM1E,gBAAgB,GAAGtE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAM4E,QAAQ,GAAGvE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAM8E,kBAAkB,GAAGD,QAAQ,IAAI5E,oBAAoB,KAAK,cAAc;EAC9E,MAAM8E,YAAY,GAAGF,QAAQ,IAAI5E,oBAAoB,KAAK,QAAQ;EAClE,MAAM+E,aAAa,GAAGH,QAAQ,KAAK5E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMmF,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;EAExC,IAAIyK,kBAAyB,GAAG,EAAE;EAElC,IAAIpG,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAkG,kBAAkB,GAAGxK,8BAA8B,CAAC4E,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAmG,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAI9K,oBAAoB,CAACiF,gBAAgB,CAAC,cAAA6F,sBAAA,eAAtCA,sBAAA,CAAwCzF,UAAU,EAAE;MACpDwF,kBAAkB,GAAG7K,oBAAoB,CAACiF,gBAAgB,CAAC,CAACI,UAAU,CAAC0F,MAAM,CAAC5E,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyDjF,oBAAoB,SAASmF,gBAAgB,GAAG,EAAE;QACnH+F,eAAe,EAAEhL,oBAAoB,CAACiF,gBAAgB,CAAC,CAACI,UAAU,CAACtD,MAAM;QACzEkJ,aAAa,EAAEJ,kBAAkB,CAAC9I,MAAM;QACxCmJ,OAAO,EAAEL,kBAAkB,CAAC3E,GAAG,CAACC,CAAC,KAAK;UAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;UAAEuH,WAAW,EAAEhF,CAAC,CAACgF;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHrG,OAAO,CAACkB,IAAI,CAAC,iDAAiDlG,oBAAoB,SAASmF,gBAAgB,EAAE,CAAC;MAC9G4F,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAGrL,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAA0M,QAAA,EACKP,kBAAkB,CAAC3E,GAAG,CAAEmF,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAInK,KAAK,GAAG,EAAE;MACd,IAAIyC,EAAE,GAAG,EAAE;MAEX,IAAKa,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACA2G,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChChK,KAAK,GAAGkK,IAAI,CAACzH,EAAE;QACfA,EAAE,GAAGyH,IAAI,CAACzH,EAAE;MAChB,CAAC,MAAM;QAAA,IAAA2H,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCzK,KAAK,IAAAsK,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB9H,EAAE;QAC1BA,EAAE,GAAGyH,IAAI,CAACzH,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAGpB,MAAMK,mBAAmB,GAAGxC,eAAe,CAACmC,EAAE,CAAC;MAC/C,MAAMiI,gBAAgB,GAAG3K,YAAY,CAAC0C,EAAE,CAAC;MAEzC,oBACIpF,OAAA,CAACT,GAAG;QAEAsB,GAAG,EAAE4E,mBAAoB;QACzB6H,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB;UACA,6BAA6B,EAAE;YAC3BC,OAAO,EAAE;UACb,CAAC;UACD,0BAA0B,EAAE;YACxBH,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfjC,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BtB,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBF,KAAK,EAAEzI,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDsM,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChB9C,SAAS,EAAEzJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACtIyJ,SAAS,EAC3C1J,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAExE,gCAAgC,GAChC,IAAI;YAC0BuM,QAAQ,EAAExM,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAE,QAAQ,GAAG;UAC5H,CAAC;UACD,kBAAkB,EAAE;YAChB0I,QAAQ,EAAC3I,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACpIwJ,SAAS,EAAEzJ,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,qBAAqB,EAAE;YACnBiM,OAAO,EAAE,iBAAiB;YAC1BO,cAAc,EAAE,mBAAmB;YACnCjD,MAAM,EAAExJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnIyJ,SAAS,EAAE1J,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBmM,QAAQ,EAAE,kBAAkB;YAC5BxC,MAAM,EAAE,mBAAmB;YAC3B8C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCR,QAAQ,EAAE,qBAAqB;YAC/BM,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BlE,QAAQ,EAAE,kBAAkB;YAC5BmE,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBZ,QAAQ,EAAE,kBAAkB;YAC5BxC,MAAM,EAAE,mBAAmB;YAC3B8C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf;QACJ,CAAE;QACFK,SAAS,EAAC,WAAW;QAAA1B,QAAA,eAGrB5M,OAAA;UAAKmL,KAAK,EAAE;YAAErB,KAAK,EAAE,MAAM;YAAE2D,QAAQ,EAAE;UAAW,CAAE;UAAAb,QAAA,gBAChD5M,OAAA,CAACP,WAAW;YACRoB,GAAG,EAAEwM,gBAAiB;YACtBkB,KAAK,EAAEzB,OAAQ;YACf0B,MAAM,EAAEvF,cAAc,CAAC7D,EAAE,CAAE;YAC3BqJ,QAAQ,EAAG1I,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEpD,KAAK,EAAEyC,EAAE;UAAE;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAGF7O,OAAA;YACIsO,SAAS,EAAC,oBAAoB;YAC9BnD,KAAK,EAAE;cACHsC,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,KAAK;cACVe,KAAK,EAAE,MAAM;cACbvB,OAAO,EAAE,MAAM;cACfwB,GAAG,EAAE,KAAK;cACVrB,OAAO,EAAE,CAAC;cACVsB,UAAU,EAAE,0BAA0B;cACtC/D,MAAM,EAAE,IAAI;cACZgE,aAAa,EAAE;YACnB,CAAE;YAAArC,QAAA,gBAGF5M,OAAA,CAACR,UAAU;cACP0P,IAAI,EAAC,OAAO;cACZC,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBzH,oBAAoB,CAACiF,IAAI,CAACzH,EAAE,CAAC;cACjC,CAAE;cACFkK,QAAQ,EAAE1O,eAAgB;cAC1B2O,KAAK,EAAE3O,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHuM,EAAE,EAAE;gBACAxD,KAAK,EAAE,MAAM;gBACbe,MAAM,EAAE,MAAM;gBACdQ,eAAe,EAAE,0BAA0B;gBAC3C4D,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE;kBACP5D,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLvB,KAAK,EAAE,MAAM;kBACbe,MAAM,EAAE;gBACZ;cACJ,CAAE;cAAA+B,QAAA,eAEF5M,OAAA;gBACIwP,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9P;gBAAS,CAAE;gBAC9CwL,KAAK,EAAE;kBACHuC,OAAO,EAAE9M,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClCiK,MAAM,EAAE,MAAM;kBACdf,KAAK,EAAE;gBACX;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGb7O,OAAA,CAACR,UAAU;cACP0P,IAAI,EAAC,OAAO;cACZC,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBxH,mBAAmB,CAACgF,IAAI,CAACzH,EAAE,EAAEzC,KAAK,CAAC;cACvC,CAAE;cACF4M,KAAK,EAAExO,SAAS,CAAC,gBAAgB,CAAE;cACnCuM,EAAE,EAAE;gBACAxD,KAAK,EAAE,MAAM;gBACbe,MAAM,EAAE,MAAM;gBACdQ,eAAe,EAAE,0BAA0B;gBAC3C4D,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE;kBACP5D,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLvB,KAAK,EAAE,MAAM;kBACbe,MAAM,EAAE;gBACZ;cACJ,CAAE;cAAA+B,QAAA,eAEF5M,OAAA;gBACIwP,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7P;gBAAW,CAAE;gBAChDuL,KAAK,EAAE;kBACHN,MAAM,EAAE,MAAM;kBACdf,KAAK,EAAE;gBACX;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGb7O,OAAA,CAACR,UAAU;cACP0P,IAAI,EAAC,OAAO;cACZC,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB3G,aAAa,CAACtD,EAAE,CAAC;cACrB,CAAE;cACFmK,KAAK,EAAExO,SAAS,CAAC,gBAAgB,CAAE;cACnCuM,EAAE,EAAE;gBACAxD,KAAK,EAAE,MAAM;gBACbe,MAAM,EAAE,MAAM;gBACdQ,eAAe,EAAE,0BAA0B;gBAC3C4D,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE;kBACP5D,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLvB,KAAK,EAAE,MAAM;kBACbe,MAAM,EAAE;gBACZ;cACJ,CAAE;cAAA+B,QAAA,eAEF5M,OAAA;gBACIwP,uBAAuB,EAAE;kBAAEC,MAAM,EAAE5P;gBAAS,CAAE;gBAC9CsL,KAAK,EAAE;kBAAEN,MAAM,EAAE,MAAM;kBAAEf,KAAK,EAAE;gBAAO;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC,GAtMDzJ,EAAE;QAAAsJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuMN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAxoB4B/O,cAAc,EAgBnCJ,cAAc;AAAA,EAynB1B,CAAC;EAAA,QAzoBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAynBzB;AAACgQ,GAAA,GA3oBIvP,UAAqC;AA6oB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAqP,GAAA;AAAAC,YAAA,CAAAtP,EAAA;AAAAsP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}